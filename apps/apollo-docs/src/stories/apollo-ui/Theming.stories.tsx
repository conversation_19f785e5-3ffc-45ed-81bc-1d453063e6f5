import { Button, createThemeV2, Theme, Typography } from "@apollo/ui";
import {
  ArgTypes,
  Description,
  Primary,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks";
import type { <PERSON>a, StoryObj } from "@storybook/react";
import { useState } from "react";

/**
 * Theme component
 *
 * The Theme component allows you to customize Apollo design tokens to satisfy UI diversity
 * from business or brand requirements, including color, typography, space, etc.
 *
 * In version 2.0, we provide enhanced theming capabilities including:
 * - Color mode theme (light/dark)
 * - Multiple themes
 * - Nested theme support
 * - Design token customization at Global, Base, and Alias levels
 */
const meta: Meta<typeof Theme> = {
  title: "@apollo/ui/Theming/Theme",
  component: Theme,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "The Theme component provides a powerful theming system for Apollo UI components. It supports nested themes, color modes, and comprehensive design token customization.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <ArgTypes />
          <Stories />
        </>
      ),
    },
  },
  argTypes: {
    theme: {
      description: "Theme configuration object containing tokens and settings",
      control: { type: "object" },
    },
    mode: {
      description: "Color mode for the theme",
      control: { type: "select" },
      options: ["light", "dark"],
    },
    children: {
      description: "Child components to be themed",
      control: { type: "text" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic usage story
export const Default: Story = {
  args: {
    children: <Button>Default Theme</Button>,
  },
  render: (args) => <Theme {...args} />,
};

// Custom design tokens story
export const CustomDesignTokens: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
      <Theme>
        <Button>Default</Button>
      </Theme>
      <Theme
        theme={createThemeV2({
          tokens: {
            // Global Token
            color: {
              "green-pine": {
                0: "#000000",
                40: "#7FDA8E",
                50: "#9BF7A7",
                70: "#F6FFF2",
                100: "#FFFFFF",
              },
            },
            // Base Token
            base: {
              color: {
                primary: {
                  0: "{color.green-pine.0}",
                  40: "{color.green-pine.40}",
                  70: "{color.green-pine.70}",
                  100: "{color.green-pine.100}",
                },
              },
            },
            // Alias Token
            alias: {
              color: {
                light: {
                  primary: {
                    primary: "{base.color.primary.40}",
                    "on-primary": "{base.color.primary.0}",
                  },
                },
                dark: {
                  primary: {
                    primary: "{base.color.primary.70}",
                    "on-primary": "{base.color.primary.100}",
                  },
                },
              },
            },
          },
        })}
      >
        <Button>Custom Tokens</Button>
      </Theme>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates how to customize design tokens at Global, Base, and Alias levels. This example shows how to override the primary color system with custom green tokens.",
      },
    },
  },
};

// Color mode story component
const ColorModeExample = () => {
  const [colorMode, setColorMode] = useState<"light" | "dark">("light");

  return (
    <div
      style={{
        display: "flex",
        gap: "16px",
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <Theme mode={colorMode}>
        <Button>{colorMode === "light" ? "Light Mode" : "Dark Mode"}</Button>
      </Theme>
      <Theme
        theme={createThemeV2({
          tokens: {
            alias: {
              color: {
                light: {
                  primary: {
                    primary: "{base.color.primary.40}",
                    "on-primary": "{base.color.primary.100}",
                  },
                },
                dark: {
                  primary: {
                    primary: "{base.color.primary.40}",
                    "on-primary": "{base.color.primary.100}",
                  },
                },
              },
            },
          },
        })}
        mode={colorMode}
      >
        <Button>Light Mode Always</Button>
      </Theme>
      <Theme mode="dark">
        <Button>Dark Mode Always</Button>
      </Theme>
      <Theme mode={colorMode}>
        <Button
          onClick={() => setColorMode(colorMode === "light" ? "dark" : "light")}
        >
          Toggle Mode
        </Button>
      </Theme>
    </div>
  );
};

export const ColorMode: Story = {
  render: () => <ColorModeExample />,
  parameters: {
    docs: {
      description: {
        story:
          "Shows how to use the mode property to switch between light and dark themes. You can set specific modes or create interactive toggles.",
      },
    },
  },
};

// Nested themes story
export const NestedThemes: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        gap: "16px",
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <Theme mode="dark">
        <div
          style={{
            padding: "16px",
            border: "1px solid #ccc",
            borderRadius: "8px",
          }}
        >
          <Typography level="bodyMedium" style={{ marginBottom: "8px" }}>
            Dark Theme Parent
          </Typography>
          <Button style={{ marginRight: "8px" }}>Hello Dark</Button>
          <Theme mode="light">
            <div
              style={{
                padding: "8px",
                border: "1px solid #eee",
                borderRadius: "4px",
                marginTop: "8px",
              }}
            >
              <Typography level="bodySmall" style={{ marginBottom: "8px" }}>
                Light Theme Child
              </Typography>
              <Button style={{ marginRight: "8px" }}>Hello Light</Button>
              <Theme>
                <Button>Hello Inherit</Button>
              </Theme>
            </div>
          </Theme>
        </div>
      </Theme>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates nested theme support. Child themes can inherit from parent themes or override specific properties. Themes that don't specify a mode will inherit from their parent.",
      },
    },
  },
};
